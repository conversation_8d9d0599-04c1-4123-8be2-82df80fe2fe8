<!-- 确认产品信息弹框组件 -->
<template>
  <wd-popup
    v-model="visible"
    position="center"
    :close-on-click-modal="false"
    :custom-style="'width:60%;'"
  >
    <view class="confirm-dialog" :class="{ 'confirm-dialog--pad': isPad }">
      <!-- 弹框标题 -->
      <view class="dialog-header">
        <view class="dialog-title">确认产品信息</view>
        <view class="dialog-subtitle">请检查产品信息是否正确</view>
      </view>

      <!-- 产品列表表格 -->
      <view class="product-table">
        <view class="table-container">
          <table class="data-table">
            <thead>
              <tr>
                <th class="table-header-cell">序号</th>
                <th class="table-header-cell">产品编号</th>
              </tr>
            </thead>
          </table>
          <view class="table-body-container">
            <table class="data-table">
              <tbody>
                <tr v-for="(product, index) in productList" :key="index" class="table-row">
                  <td class="table-cell">{{ index + 1 }}</td>
                  <td class="table-cell">{{ product }}</td>
                </tr>
              </tbody>
            </table>
          </view>
        </view>
      </view>

      <!-- 统计信息 -->
      <view class="summary-info">
        <view class="summary-item">
          <text class="summary-label">产品总数：</text>
          <text class="summary-value">{{ productList.length }} 件</text>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="dialog-actions">
        <wd-button size="large" :custom-style="'flex:1;marginRight:5%;'" @click="handleCancel">
          取消
        </wd-button>
        <wd-button
          type="primary"
          size="large"
          :loading="loading"
          :custom-style="'flex:1;marginRight:5%;'"
          @click="handleConfirm"
        >
          确认装箱
        </wd-button>
      </view>
    </view>
  </wd-popup>
</template>

<script lang="ts" setup>
import { computed } from 'vue'

interface Props {
  modelValue: boolean
  productList: string[]
  loading?: boolean
}

interface Emits {
  (e: 'update:modelValue', value: boolean): void
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
})

const emit = defineEmits<Emits>()

// 计算属性：弹框显示状态
const visible = computed({
  get: () => props.modelValue,
  set: (value: boolean) => emit('update:modelValue', value),
})

// 检测是否为 pad 端
const isPad = computed(() => {
  const { screenWidth } = uni.getSystemInfoSync()
  return screenWidth >= 768
})

// 事件处理
const handleCancel = () => {
  emit('cancel')
}

const handleConfirm = () => {
  emit('confirm')
}
</script>

<style scoped>
.confirm-dialog {
  display: flex;
  flex-direction: column;
  max-height: 85vh;
  overflow: hidden;
  background: #ffffff;
  border-radius: 12px;
}

.confirm-dialog--pad {
  border-radius: 16px;
}

.dialog-header {
  padding: 24px 24px 16px;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.dialog-title {
  margin-bottom: 4px;
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
}

.dialog-subtitle {
  font-size: 14px;
  color: #666666;
}

.product-table {
  flex: 1;
  margin: 16px 24px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  overflow: hidden;
}

.data-table {
  width: 100%;
  border-collapse: collapse;
}

.data-table thead {
  background: #f8f9fa;
}

.table-header-cell {
  padding: 12px 8px;
  font-size: 14px;
  font-weight: 600;
  color: #1a1a1a;
  text-align: center;
  background: #f8f9fa;
  border-bottom: 1px solid #e9ecef;
}

.table-body-container {
  flex: 1;
  overflow-y: auto;
  max-height: 300px; /* 增加最大高度 */
}

.table-row {
  border-bottom: 1px solid #f0f0f0;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background: #f8f9fa;
}

.table-cell {
  padding: 12px 8px;
  font-size: 14px;
  color: #333333;
  text-align: center;
  border-bottom: 1px solid #f0f0f0;
}

.table-cell:first-child {
  width: 30%;
}

.table-cell:last-child {
  width: 70%;
}

.summary-info {
  padding: 16px 24px;
  background: #f8f9fa;
  border-top: 1px solid #e9ecef;
  flex-shrink: 0;
}

.summary-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.summary-label {
  font-size: 14px;
  color: #666666;
}

.summary-value {
  font-size: 16px;
  font-weight: 600;
  color: #1890ff;
}

.dialog-actions {
  display: flex;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #f0f0f0;
  flex-shrink: 0;
}

/* pad 端样式调整 */
.confirm-dialog--pad .dialog-header {
  padding: 32px 32px 20px;
}

.confirm-dialog--pad .dialog-title {
  font-size: 20px;
}

.confirm-dialog--pad .product-table {
  margin: 20px 32px;
}

.confirm-dialog--pad .table-body-container {
  max-height: 400px; /* pad端增加更多高度 */
}

.confirm-dialog--pad .table-header-cell {
  padding: 16px 12px;
  font-size: 15px;
}

.confirm-dialog--pad .table-cell {
  padding: 16px 12px;
  font-size: 15px;
}

.confirm-dialog--pad .summary-info {
  padding: 20px 32px;
}

.confirm-dialog--pad .dialog-actions {
  gap: 16px;
  padding: 24px 32px;
}

/* 滚动条样式 */
.table-body-container::-webkit-scrollbar {
  width: 6px;
}

.table-body-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.table-body-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-body-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
