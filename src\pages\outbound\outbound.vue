<!--
  出库校验页面 - 用于验证KD件码和随箱卡条码的匹配性
  主要功能：
  1. 扫描或手动输入KD件码
  2. 扫描或手动输入随箱卡条码
  3. 对比两个码的信息是否匹配
  4. 确认出库操作
-->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '出库校验', // 页面标题
    navigationBarBackgroundColor: '#ffffff', // 导航栏背景色
    navigationBarTextStyle: 'black', // 导航栏文字颜色
  },
}
</route>
<template>
  <!-- 出库校验页面主容器 - 使用灰色背景，最小高度为屏幕高度 -->
  <view class="outbound-page bg-gray-50 min-h-screen">
    <!-- KD件码输入区域 - 用于输入或扫描KD件码 -->
    <view class="code-input-section bg-white p-4 mb-2">
      <!-- 区域标题 - 带有蓝色左边框的标题样式 -->
      <view class="section-title text-base font-medium text-gray-800 mb-3">KD件码</view>
      <!-- 输入框和扫码按钮的容器 - 使用flex布局水平排列 -->
      <view class="input-container flex items-center gap-2 mb-2">
        <!-- KD件码输入框 - 支持手动输入，绑定kdCode响应式数据 -->
        <wd-input
          v-model="kdCode"
          placeholder="请扫描或输入KD件码"
          class="flex-1"
          clearable
          @confirm="onKdCodeInput"
          @blur="onKdCodeInput"
        />
        <!-- 扫码按钮 - 点击后调用扫码功能，扫码时显示loading状态 -->
        <!-- <wd-button type="primary" size="small" :loading="scanningKd" @click="scanKdCode">
          <wd-icon name="scan" size="16px" class="mr-1"></wd-icon>
          扫码
        </wd-button> -->
      </view>
    </view>

    <!-- 随箱卡条码输入区域 - 用于输入或扫描随箱卡条码 -->
    <view class="code-input-section bg-white p-4 mb-4">
      <!-- 区域标题 - 带有蓝色左边框的标题样式 -->
      <view class="section-title text-base font-medium text-gray-800 mb-3">客户随箱卡条码</view>
      <!-- 输入框和扫码按钮的容器 - 使用flex布局水平排列 -->
      <view class="input-container flex items-center gap-2 mb-2">
        <!-- 随箱卡条码输入框 - 支持手动输入，绑定boxCardCode响应式数据 -->
        <wd-input
          v-model="boxCardCode"
          placeholder="请扫描或输入随箱卡条码"
          class="flex-1"
          clearable
          @confirm="onBoxCardCodeInput"
          @blur="onBoxCardCodeInput"
        />
        <!-- 扫码按钮 - 点击后调用扫码功能，扫码时显示loading状态 -->
        <!-- <wd-button type="primary" size="small" :loading="scanningBoxCard" @click="scanBoxCardCode">
          <wd-icon name="scan" size="16px" class="mr-1"></wd-icon>
          扫码
        </wd-button> -->
      </view>
    </view>

    <!-- 信息对比区域 - 显示KD码和随箱卡的详细信息，用于对比验证 -->
    <view class="comparison-section bg-white p-4 mb-4">
      <!-- 使用栅格布局，左右两列显示信息，列间距为16px -->
      <wd-row :gutter="16">
        <!-- KD码信息卡片 - 左侧列，占12格（总共24格） -->
        <wd-col :span="12">
          <view class="info-card">
            <!-- KD码信息标题 - 蓝色字体 -->
            <view class="info-title text-blue-600 font-medium mb-3">KD码信息</view>
            <!-- 客户零件号信息项 - 对应解析码中的key 10 -->
            <view class="info-item">
              <view class="info-label">客户零件号</view>
              <!-- 只读输入框，显示从kdCodeInfo响应式数据中获取的客户零件号 -->
              <wd-input v-model="kdCodeInfo.partCode" placeholder="" readonly class="info-input" />
            </view>
            <!-- 单包数量信息项 - 对应解析码中的key 17 -->
            <view class="info-item">
              <view class="info-label">单包数量</view>
              <!-- 只读输入框，显示从kdCodeInfo响应式数据中获取的单包数量 -->
              <wd-input
                v-model="kdCodeInfo.singleQuantity"
                placeholder=""
                readonly
                class="info-input"
              />
            </view>
            <!-- 供货批次号信息项 - 对应解析码中的key 18 -->
            <view class="info-item">
              <view class="info-label">供货批次号</view>
              <!-- 只读输入框，显示从kdCodeInfo响应式数据中获取的供货批次号 -->
              <wd-input v-model="kdCodeInfo.lotNo" placeholder="" readonly class="info-input" />
            </view>
          </view>
        </wd-col>

        <!-- 随箱卡信息卡片 - 右侧列，占12格（总共24格） -->
        <wd-col :span="12">
          <view class="info-card">
            <!-- 随箱卡信息标题 - 蓝色字体 -->
            <view class="info-title text-blue-600 font-medium mb-3">随箱卡信息</view>
            <!-- 客户零件号信息项 - 对应解析码中的key 10 -->
            <view class="info-item">
              <view class="info-label">客户零件号</view>
              <!-- 只读输入框，显示从boxCardInfo响应式数据中获取的客户零件号 -->
              <wd-input v-model="boxCardInfo.partCode" placeholder="" readonly class="info-input" />
            </view>
            <!-- 单包数量信息项 - 对应解析码中的key 17 -->
            <view class="info-item">
              <view class="info-label">单包数量</view>
              <!-- 只读输入框，显示从boxCardInfo响应式数据中获取的单包数量 -->
              <wd-input
                v-model="boxCardInfo.singleQuantity"
                placeholder=""
                readonly
                class="info-input"
              />
            </view>
            <!-- 供货批次号信息项 - 对应解析码中的key 18 -->
            <view class="info-item">
              <view class="info-label">供货批次号</view>
              <!-- 只读输入框，显示从boxCardInfo响应式数据中获取的供货批次号 -->
              <wd-input v-model="boxCardInfo.lotNo" placeholder="" readonly class="info-input" />
            </view>
          </view>
        </wd-col>
      </wd-row>

      <!-- 对比结果显示区域 - 只有当comparisonResult有值时才显示 -->
      <view v-if="comparisonResult" class="comparison-result mt-4 text-center">
        <!-- 结果徽章 - 根据匹配结果显示不同颜色（绿色=匹配，红色=不匹配） -->
        <view
          class="result-badge px-4 py-2 rounded-full text-white font-medium"
          :class="comparisonResult.isMatch ? 'bg-green-500' : 'bg-red-500'"
        >
          <!-- 结果图标 - 匹配显示对勾，不匹配显示叉号 -->
          <wd-icon
            :name="comparisonResult.isMatch ? 'check' : 'close'"
            size="16px"
            class="mr-1"
          ></wd-icon>
          <!-- 显示对比结果消息 -->
          {{ comparisonResult.message }}
        </view>
      </view>
    </view>

    <!-- 确认出库按钮区域 - 页面底部的操作区域 -->
    <view class="action-section p-4">
      <!-- 确认出库按钮 - 大尺寸、全宽度、主要样式 -->
      <wd-button
        type="primary"
        size="large"
        block
        :loading="confirming"
        :disabled="!canConfirm"
        @click="confirmOutbound"
      >
        确认出库
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
// 导入 wot-design-uni 的 toast 组件，用于显示提示消息
import { useToast } from 'wot-design-uni'
// 导入出库相关的 TypeScript 类型定义
import type { KdCodeInfo, BoxCardInfo, OutboundVerifyResponse } from '@/types/outbound'
// 导入出库相关的 API 服务函数
import {
  confirmOutbound as submitOutbound, // 确认出库的API（重命名为submitOutbound避免命名冲突）
  simulateScanCode, // 模拟扫码功能的API
} from '@/service/outbound'
import { checkKdCodeApi, savePassRecordApi } from '@/service/outbound/index'
// 格式化kdcode方法
import { parseCodeToObject } from '@/utils/parseCode'

// 定义组件选项，设置组件名称
defineOptions({
  name: 'OutboundPage',
})

// 初始化 toast 实例，用于显示各种提示消息
const toast = useToast()

// ==================== 响应式数据定义 ====================

// KD件码输入值 - 用户输入或扫描的KD件码字符串
const kdCode = ref<string>('')

// 随箱卡条码输入值 - 用户输入或扫描的随箱卡条码字符串
const boxCardCode = ref<string>('')

// KD码详细信息 - 根据KD码解析得到的详细信息对象
const kdCodeInfo = ref<KdCodeInfo>({
  partCode: '', // 客户零件号 - 对应解析码中的key 10
  singleQuantity: '', // 单包数量 - 对应解析码中的key 17
  lotNo: '', // 供货批次号 - 对应解析码中的key 18
})

// 随箱卡详细信息 - 根据随箱卡码解析得到的详细信息对象
const boxCardInfo = ref<BoxCardInfo>({
  partCode: '', // 客户零件号 - 对应解析码中的key 10
  singleQuantity: '', // 单包数量 - 对应解析码中的key 17
  lotNo: '', // 供货批次号 - 对应解析码中的key 18
})

// 对比结果 - 存储KD码和随箱卡信息对比的结果，包含是否匹配和提示消息
const comparisonResult = ref<OutboundVerifyResponse | null>(null)

// KD码扫描状态 - 控制KD码扫描按钮的loading状态
const scanningKd = ref<boolean>(false)

// 随箱卡扫描状态 - 控制随箱卡扫描按钮的loading状态
const scanningBoxCard = ref<boolean>(false)

// 确认出库状态 - 控制确认出库按钮的loading状态
const confirming = ref<boolean>(false)

// ==================== 计算属性定义 ====================

// 计算属性：已扫描的KD码显示文本
// 如果用户已输入KD码则显示输入的值，否则显示默认的示例文本
const kdCodeScanned = computed(() => {
  return kdCode.value || 'iPhone 13 Pro Max (256GB)'
})

// 计算属性：已扫描的随箱卡码显示文本
// 如果用户已输入随箱卡码则显示输入的值，否则显示默认的示例文本
const boxCardCodeScanned = computed(() => {
  return boxCardCode.value || 'iPhone 13 Pro Max (256GB)'
})

// 计算属性：是否可以确认出库
// 需要同时满足：1.有KD码 2.有随箱卡码 3.对比结果匹配
const canConfirm = computed(() => {
  return kdCode.value && boxCardCode.value && comparisonResult.value?.isMatch
})

// ==================== 事件处理方法 ====================

/**
 * 处理KD码输入事件（手动输入或扫描后触发）
 * 功能：
 * 1. 验证输入是否为空，为空则清空相关信息
 * 2. 使用parseCode解析KD码获取详细信息
 * 3. 更新kdCodeInfo响应式数据
 * 4. 如果随箱卡码也已输入，则自动进行信息对比
 */
const onKdCodeInput = async () => {
  // 先调用检查KD码的API
  const res = await checkKdCodeApi(kdCode.value.trim())
  if (res.data === 1) {
    toast.warning({ msg: res.msg, duration: 2000, iconName: 'warning', iconSize: 50 })
    return
  } else if (res.data === 2) {
    toast.warning({ msg: res.msg, duration: 2000, iconName: 'warning', iconSize: 50 })
    return
  }

  // 检查输入是否为空（去除首尾空格后）
  if (!kdCode.value.trim()) {
    // 如果输入为空，清空KD码相关信息和对比结果
    kdCodeInfo.value = {
      partCode: '',
      singleQuantity: '',
      lotNo: '',
    }
    comparisonResult.value = null
    return
  }

  try {
    // 使用parseCode解析KD码获取详细信息
    const kdCodeObj = parseCodeToObject(kdCode.value.trim())
    console.log('KD码解析对象', kdCodeObj)

    // 根据解析结果更新KD码信息
    kdCodeInfo.value = {
      partCode: kdCodeObj[10] || '', // 客户零件号 - key 10
      singleQuantity: kdCodeObj[17] || '', // 单包数量 - key 17
      lotNo: kdCodeObj[18] || '', // 供货批次号 - key 18
      kdCode: kdCode.value.trim(),
    }

    // 检查是否成功解析到关键信息
    if (kdCodeInfo.value.partCode || kdCodeInfo.value.singleQuantity || kdCodeInfo.value.lotNo) {
      toast.show('KD码解析成功')
    } else {
      toast.show('KD码格式不正确，未能解析到有效信息')
    }

    // 如果随箱卡码也已经输入，则自动触发信息对比
    if (boxCardCode.value.trim()) {
      performComparison()
    }
  } catch (error) {
    // 捕获解析异常，记录错误日志并显示用户友好提示
    console.error('解析KD码失败:', error)
    toast.show('KD码解析失败')

    // 设置空信息但保留输入的码值
    kdCodeInfo.value = {
      partCode: '',
      singleQuantity: '',
      lotNo: '',
      kdCode: kdCode.value.trim(),
    }
  }
}

/**
 * 处理随箱卡码输入事件（手动输入或扫描后触发）
 * 功能：
 * 1. 验证输入是否为空，为空则清空相关信息
 * 2. 使用parseCode解析随箱卡码获取详细信息
 * 3. 更新boxCardInfo响应式数据
 * 4. 如果KD码也已输入，则自动进行信息对比
 */
const onBoxCardCodeInput = async () => {
  // 检查输入是否为空（去除首尾空格后）
  if (!boxCardCode.value.trim()) {
    // 如果输入为空，清空随箱卡相关信息和对比结果
    boxCardInfo.value = {
      partCode: '',
      singleQuantity: '',
      lotNo: '',
    }
    comparisonResult.value = null
    return
  }

  try {
    // 使用parseCode解析随箱卡码获取详细信息
    const boxCardObj = parseCodeToObject(boxCardCode.value.trim())
    console.log('随箱卡码解析对象', boxCardObj)

    // 根据解析结果更新随箱卡信息
    boxCardInfo.value = {
      partCode: boxCardObj[10] || '', // 客户零件号 - key 10
      singleQuantity: boxCardObj[17] || '', // 单包数量 - key 17
      lotNo: boxCardObj[18] || '', // 供货批次号 - key 18
      boxCardCode: boxCardCode.value.trim(),
    }

    // 检查是否成功解析到关键信息
    if (boxCardInfo.value.partCode || boxCardInfo.value.singleQuantity || boxCardInfo.value.lotNo) {
      toast.show('随箱卡码解析成功')
    } else {
      toast.show('随箱卡码格式不正确，未能解析到有效信息')
    }

    // 如果KD码也已经输入，则自动触发信息对比
    if (kdCode.value.trim()) {
      performComparison()
    }
  } catch (error) {
    // 捕获解析异常，记录错误日志并显示用户友好提示
    console.error('解析随箱卡码失败:', error)
    toast.show('随箱卡码解析失败')

    // 设置空信息但保留输入的码值
    boxCardInfo.value = {
      partCode: '',
      singleQuantity: '',
      lotNo: '',
      boxCardCode: boxCardCode.value.trim(),
    }
  }
}

/**
 * 扫描KD码功能
 * 功能：
 * 1. 设置扫描状态为loading
 * 2. 调用模拟扫码API获取KD码
 * 3. 将扫描结果设置到kdCode响应式数据
 * 4. 自动调用KD码输入处理函数
 * 5. 显示扫描成功提示
 */
const scanKdCode = async () => {
  try {
    // 设置KD码扫描按钮为loading状态
    scanningKd.value = true

    // 调用模拟扫码API，传入'kd'参数表示扫描KD码
    const scannedCode = await simulateScanCode('kd')

    // 将扫描到的码值设置到KD码输入框
    kdCode.value = scannedCode

    // 自动调用KD码输入处理函数，获取详细信息
    await onKdCodeInput()

    // 显示扫描成功提示
    toast.show('KD码扫描成功')
  } catch (error) {
    // 捕获扫码异常，记录错误日志并显示用户友好提示
    console.error('扫描KD码失败:', error)
    toast.show('扫描失败')
  } finally {
    // 无论成功失败都要取消loading状态
    scanningKd.value = false
  }
}

/**
 * 扫描随箱卡码功能
 * 功能：
 * 1. 设置扫描状态为loading
 * 2. 调用模拟扫码API获取随箱卡码
 * 3. 将扫描结果设置到boxCardCode响应式数据
 * 4. 自动调用随箱卡码输入处理函数
 * 5. 显示扫描成功提示
 */
const scanBoxCardCode = async () => {
  try {
    // 设置随箱卡扫描按钮为loading状态
    scanningBoxCard.value = true

    // 调用模拟扫码API，传入'boxcard'参数表示扫描随箱卡码
    const scannedCode = await simulateScanCode('boxcard')

    // 将扫描到的码值设置到随箱卡码输入框
    boxCardCode.value = scannedCode

    // 自动调用随箱卡码输入处理函数，获取详细信息
    await onBoxCardCodeInput()

    // 显示扫描成功提示
    toast.show('随箱卡码扫描成功')
  } catch (error) {
    // 捕获扫码异常，记录错误日志并显示用户友好提示
    console.error('扫描随箱卡码失败:', error)
    toast.show('扫描失败')
  } finally {
    // 无论成功失败都要取消loading状态
    scanningBoxCard.value = false
  }
}

/**
 * 执行KD码和随箱卡信息对比
 * 功能：
 * 1. 验证两个码都已输入
 * 2. 直接对比解析后的信息（客户零件号、单包数量、供货批次号）
 * 3. 更新对比结果
 * 4. 处理对比异常情况
 */
const performComparison = () => {
  // 检查两个码是否都已输入，如果任一为空则不执行对比
  if (!kdCode.value || !boxCardCode.value) return

  try {
    // 检查是否都有解析到的信息
    const hasKdInfo =
      kdCodeInfo.value.partCode || kdCodeInfo.value.singleQuantity || kdCodeInfo.value.lotNo
    const hasBoxCardInfo =
      boxCardInfo.value.partCode || boxCardInfo.value.singleQuantity || boxCardInfo.value.lotNo

    if (!hasKdInfo || !hasBoxCardInfo) {
      comparisonResult.value = {
        isMatch: false,
        message: '码值解析不完整，无法进行对比',
        kdCodeInfo: kdCodeInfo.value,
        boxCardInfo: boxCardInfo.value,
      }
      return
    }

    // 对比三个关键字段：客户零件号、单包数量、供货批次号
    const partCodeMatch = kdCodeInfo.value.partCode === boxCardInfo.value.partCode
    const quantityMatch = kdCodeInfo.value.singleQuantity === boxCardInfo.value.singleQuantity
    const lotNoMatch = kdCodeInfo.value.lotNo === boxCardInfo.value.lotNo.split('~')[0]

    // 所有字段都匹配才算成功
    const isMatch = partCodeMatch && quantityMatch && lotNoMatch

    // 生成详细的对比结果消息
    let message = ''
    if (isMatch) {
      message = '信息匹配成功，可以出库'
    } else {
      const mismatches = []
      if (!partCodeMatch) mismatches.push('客户零件号不匹配')
      if (!quantityMatch) mismatches.push('单包数量不匹配')
      if (!lotNoMatch) mismatches.push('供货批次号不匹配')
      message = `信息不匹配：${mismatches.join('、')}`
    }

    // 更新对比结果
    comparisonResult.value = {
      isMatch,
      message,
      kdCodeInfo: kdCodeInfo.value,
      boxCardInfo: boxCardInfo.value,
    }

    console.log('对比结果:', {
      partCodeMatch,
      quantityMatch,
      lotNoMatch,
      isMatch,
      message,
    })
  } catch (error) {
    // 捕获对比异常，记录错误日志并显示用户友好提示
    console.error('比对失败:', error)
    toast.show('比对失败')

    comparisonResult.value = {
      isMatch: false,
      message: '对比过程出现异常',
      kdCodeInfo: kdCodeInfo.value,
      boxCardInfo: boxCardInfo.value,
    }
  }
}

/**
 * 确认出库操作
 * 功能：
 * 1. 验证是否满足确认出库的条件
 * 2. 设置确认按钮为loading状态
 * 3. 调用后端确认出库API
 * 4. 处理出库结果并显示相应提示
 * 5. 成功后延时重置页面数据
 */
const confirmOutbound = async () => {
  // 检查是否满足确认出库的条件（通过canConfirm计算属性）
  if (!canConfirm.value) return

  try {
    // 设置确认出库按钮为loading状态
    confirming.value = true

    // 调用后端确认出库API，传入必要的出库信息
    const response = await savePassRecordApi({
      kdCode: kdCode.value, // KD件码
      consumerCode: boxCardCode.value, // 随箱卡条码
    })

    // 检查API响应是否成功
    if (response.data) {
      // 显示出库成功提示
      toast.show('出库成功')
      // 清空表单
      resetPage()
    } else {
      // API返回失败，显示具体的错误消息
      toast.show(response.msg)
    }
  } catch (error) {
    // 捕获出库API异常，记录错误日志并显示用户友好提示
    console.error('出库失败:', error)
    toast.show('出库失败')
  } finally {
    // 无论成功失败都要取消确认按钮的loading状态
    confirming.value = false
  }
}

/**
 * 重置页面数据
 * 功能：将所有响应式数据重置为初始状态
 * 使用场景：
 * 1. 出库成功后自动重置
 * 2. 用户手动清空数据时调用
 */
const resetPage = () => {
  // 清空KD件码输入
  kdCode.value = ''
  // 清空随箱卡条码输入
  boxCardCode.value = ''
  // 重置KD码详细信息为空对象
  kdCodeInfo.value = {
    partCode: '',
    singleQuantity: '',
    lotNo: '',
  }
  // 重置随箱卡详细信息为空对象
  boxCardInfo.value = {
    partCode: '',
    singleQuantity: '',
    lotNo: '',
  }
  // 清空对比结果
  comparisonResult.value = null
}

// ==================== 生命周期钩子 ====================

/**
 * 页面挂载完成后的初始化操作
 * 功能：页面加载时的初始化逻辑
 * 注意：这里不预填充任何数据，让用户真正体验扫描或输入功能
 */
onMounted(() => {
  // 输出调试信息，提示用户页面已准备就绪
  console.log('出库校验页面已加载，请扫描或输入KD件码和随箱卡码')
})
</script>

<!--
  页面样式定义 - 使用scoped确保样式只作用于当前组件
  主要包含：
  1. 页面整体布局样式
  2. 区域标题样式
  3. 信息卡片样式
  4. 对比结果样式
-->
<style scoped>
/* 出库页面主容器样式 - 底部留出100px空间避免内容被遮挡 */
.outbound-page {
  padding-bottom: 100px;
}

/* 区域标题样式 - 左侧蓝色边框装饰，增强视觉层次 */
.section-title {
  padding-left: 8px; /* 左侧内边距，与边框保持距离 */
  border-left: 4px solid #3b82f6; /* 4px宽度的蓝色左边框 */
}

/* 已扫描信息显示样式 - 轻微的左侧内边距 */
.scanned-info {
  padding-left: 4px;
}

/* 信息卡片容器样式 - 浅灰背景，圆角边框，营造卡片效果 */
.info-card {
  padding: 16px; /* 内边距16px */
  background-color: #fafafa; /* 浅灰色背景 */
  border: 1px solid #e5e7eb; /* 浅灰色边框 */
  border-radius: 8px; /* 8px圆角 */
}

/* 信息卡片标题样式 - 16px字体大小 */
.info-title {
  font-size: 16px;
}

/* 信息项容器样式 - 底部间距12px */
.info-item {
  margin-bottom: 12px;
}

/* 最后一个信息项样式 - 移除底部间距 */
.info-item:last-child {
  margin-bottom: 0;
}

/* 信息标签样式 - 小字体，灰色文字，用于字段名称显示 */
.info-label {
  margin-bottom: 4px; /* 底部间距4px */
  font-size: 12px; /* 12px小字体 */
  color: #666666; /* 灰色文字 */
}

/* 信息输入框样式 - 白色背景，与卡片背景形成对比 */
.info-input {
  background-color: #ffffff;
}

/* 对比结果区域样式 - 顶部边框分隔，上方内边距 */
.comparison-result {
  padding-top: 16px; /* 顶部内边距16px */
  border-top: 1px solid #e5e7eb; /* 顶部分隔线 */
}

/* 结果徽章样式 - 行内弹性布局，垂直居中对齐 */
.result-badge {
  display: inline-flex; /* 行内弹性布局 */
  align-items: center; /* 垂直居中对齐 */
  font-size: 14px; /* 14px字体大小 */
}
</style>
