<!-- 出库校验页面 -->
<route lang="json5">
{
  style: {
    navigationBarTitleText: '出库校验',
    navigationBarBackgroundColor: '#ffffff',
    navigationBarTextStyle: 'black',
  },
}
</route>
<template>
  <view class="outbound-page bg-gray-50 min-h-screen">
    <!-- KD件码输入区域 -->
    <view class="code-input-section bg-white p-4 mb-2">
      <view class="section-title text-base font-medium text-gray-800 mb-3">KD件码</view>
      <view class="input-container flex items-center gap-2 mb-2">
        <wd-input
          v-model="kdCode"
          placeholder="请扫描或输入KD件码"
          class="flex-1"
          @confirm="onKdCodeInput"
          @blur="onKdCodeInput"
        />
        <wd-button type="primary" size="small" :loading="scanningKd" @click="scanKdCode">
          <wd-icon name="scan" size="16px" class="mr-1"></wd-icon>
          扫码
        </wd-button>
      </view>
      <view class="scanned-info text-sm text-gray-600">已扫描：{{ kdCodeScanned }}</view>
    </view>

    <!-- 随箱卡条码输入区域 -->
    <view class="code-input-section bg-white p-4 mb-4">
      <view class="section-title text-base font-medium text-gray-800 mb-3">随箱卡条码</view>
      <view class="input-container flex items-center gap-2 mb-2">
        <wd-input
          v-model="boxCardCode"
          placeholder="请扫描或输入随箱卡条码"
          class="flex-1"
          @confirm="onBoxCardCodeInput"
          @blur="onBoxCardCodeInput"
        />
        <wd-button type="primary" size="small" :loading="scanningBoxCard" @click="scanBoxCardCode">
          <wd-icon name="scan" size="16px" class="mr-1"></wd-icon>
          扫码
        </wd-button>
      </view>
      <view class="scanned-info text-sm text-gray-600">已扫描：{{ boxCardCodeScanned }}</view>
    </view>

    <!-- 信息对比区域 -->
    <view class="comparison-section bg-white p-4 mb-4">
      <wd-row :gutter="16">
        <!-- KD码信息 -->
        <wd-col :span="12">
          <view class="info-card">
            <view class="info-title text-blue-600 font-medium mb-3">KD码信息</view>
            <view class="info-item">
              <view class="info-label">项目代码</view>
              <wd-input
                v-model="kdCodeInfo.projectCode"
                placeholder="PRJ-2025-001"
                readonly
                class="info-input"
              />
            </view>
            <view class="info-item">
              <view class="info-label">批次码</view>
              <wd-input
                v-model="kdCodeInfo.batchCode"
                placeholder="BATCH-001"
                readonly
                class="info-input"
              />
            </view>
            <view class="info-item">
              <view class="info-label">收货地址</view>
              <wd-input
                v-model="kdCodeInfo.deliveryAddress"
                placeholder="例：上海市浦东新区xx科技园区"
                readonly
                class="info-input"
              />
            </view>
          </view>
        </wd-col>

        <!-- 随箱卡信息 -->
        <wd-col :span="12">
          <view class="info-card">
            <view class="info-title text-blue-600 font-medium mb-3">随箱卡信息</view>
            <view class="info-item">
              <view class="info-label">项目代码</view>
              <wd-input
                v-model="boxCardInfo.projectCode"
                placeholder="PRJ-2025-001"
                readonly
                class="info-input"
              />
            </view>
            <view class="info-item">
              <view class="info-label">批次码</view>
              <wd-input
                v-model="boxCardInfo.batchCode"
                placeholder="BATCH-001"
                readonly
                class="info-input"
              />
            </view>
            <view class="info-item">
              <view class="info-label">收货地址</view>
              <wd-input
                v-model="boxCardInfo.deliveryAddress"
                placeholder="例：上海市浦东新区xx科技园区"
                readonly
                class="info-input"
              />
            </view>
          </view>
        </wd-col>
      </wd-row>

      <!-- 对比结果 -->
      <view v-if="comparisonResult" class="comparison-result mt-4 text-center">
        <view
          class="result-badge px-4 py-2 rounded-full text-white font-medium"
          :class="comparisonResult.isMatch ? 'bg-green-500' : 'bg-red-500'"
        >
          <wd-icon
            :name="comparisonResult.isMatch ? 'check' : 'close'"
            size="16px"
            class="mr-1"
          ></wd-icon>
          {{ comparisonResult.message }}
        </view>
      </view>
    </view>

    <!-- 确认出库按钮 -->
    <view class="action-section p-4">
      <wd-button
        type="primary"
        size="large"
        block
        :loading="confirming"
        :disabled="!canConfirm"
        @click="confirmOutbound"
      >
        确认出库
      </wd-button>
    </view>
  </view>
</template>

<script lang="ts" setup>
import { useToast } from 'wot-design-uni'
import type { KdCodeInfo, BoxCardInfo, OutboundVerifyResponse } from '@/types/outbound'
import {
  getKdCodeInfo,
  getBoxCardInfo,
  verifyOutbound,
  confirmOutbound as submitOutbound,
  simulateScanCode,
} from '@/service/outbound'

defineOptions({
  name: 'OutboundPage',
})

const toast = useToast()

// 响应式数据
const kdCode = ref<string>('')
const boxCardCode = ref<string>('')
const kdCodeInfo = ref<KdCodeInfo>({
  projectCode: '',
  batchCode: '',
  deliveryAddress: '',
})
const boxCardInfo = ref<BoxCardInfo>({
  projectCode: '',
  batchCode: '',
  deliveryAddress: '',
})
const comparisonResult = ref<OutboundVerifyResponse | null>(null)
const scanningKd = ref<boolean>(false)
const scanningBoxCard = ref<boolean>(false)
const confirming = ref<boolean>(false)

// 计算属性：已扫描的码显示
const kdCodeScanned = computed(() => {
  return kdCode.value || 'iPhone 13 Pro Max (256GB)'
})

const boxCardCodeScanned = computed(() => {
  return boxCardCode.value || 'iPhone 13 Pro Max (256GB)'
})

// 计算属性：是否可以确认出库
const canConfirm = computed(() => {
  return kdCode.value && boxCardCode.value && comparisonResult.value?.isMatch
})

// 处理KD码输入（手动输入或扫描后）
const onKdCodeInput = async () => {
  if (!kdCode.value.trim()) {
    // 如果输入为空，清空信息
    kdCodeInfo.value = {
      projectCode: '',
      batchCode: '',
      deliveryAddress: '',
    }
    comparisonResult.value = null
    return
  }

  try {
    // 获取KD码信息
    const response = await getKdCodeInfo(kdCode.value.trim())
    if (response.success && response.data) {
      kdCodeInfo.value = response.data
      toast.show('KD码信息获取成功')
    } else {
      kdCodeInfo.value = {
        projectCode: '',
        batchCode: '',
        deliveryAddress: '',
        kdCode: kdCode.value.trim(),
      }
      toast.show('KD码不存在或无效')
    }

    // 如果两个码都有了，进行比对
    if (boxCardCode.value.trim()) {
      await performComparison()
    }
  } catch (error) {
    console.error('获取KD码信息失败:', error)
    toast.show('获取信息失败')
  }
}

// 处理随箱卡码输入（手动输入或扫描后）
const onBoxCardCodeInput = async () => {
  if (!boxCardCode.value.trim()) {
    // 如果输入为空，清空信息
    boxCardInfo.value = {
      projectCode: '',
      batchCode: '',
      deliveryAddress: '',
    }
    comparisonResult.value = null
    return
  }

  try {
    // 获取随箱卡信息
    const response = await getBoxCardInfo(boxCardCode.value.trim())
    if (response.success && response.data) {
      boxCardInfo.value = response.data
      toast.show('随箱卡信息获取成功')
    } else {
      boxCardInfo.value = {
        projectCode: '',
        batchCode: '',
        deliveryAddress: '',
        boxCardCode: boxCardCode.value.trim(),
      }
      toast.show('随箱卡码不存在或无效')
    }

    // 如果两个码都有了，进行比对
    if (kdCode.value.trim()) {
      await performComparison()
    }
  } catch (error) {
    console.error('获取随箱卡信息失败:', error)
    toast.show('获取信息失败')
  }
}

// 扫描KD码
const scanKdCode = async () => {
  try {
    scanningKd.value = true
    const scannedCode = await simulateScanCode('kd')
    kdCode.value = scannedCode

    // 调用输入处理函数
    await onKdCodeInput()

    toast.show('KD码扫描成功')
  } catch (error) {
    console.error('扫描KD码失败:', error)
    toast.show('扫描失败')
  } finally {
    scanningKd.value = false
  }
}

// 扫描随箱卡码
const scanBoxCardCode = async () => {
  try {
    scanningBoxCard.value = true
    const scannedCode = await simulateScanCode('boxcard')
    boxCardCode.value = scannedCode

    // 调用输入处理函数
    await onBoxCardCodeInput()

    toast.show('随箱卡码扫描成功')
  } catch (error) {
    console.error('扫描随箱卡码失败:', error)
    toast.show('扫描失败')
  } finally {
    scanningBoxCard.value = false
  }
}

// 执行比对
const performComparison = async () => {
  if (!kdCode.value || !boxCardCode.value) return

  try {
    const response = await verifyOutbound({
      kdCode: kdCode.value,
      boxCardCode: boxCardCode.value,
    })

    if (response.success) {
      comparisonResult.value = response.data
      kdCodeInfo.value = response.data.kdCodeInfo
      boxCardInfo.value = response.data.boxCardInfo
    }
  } catch (error) {
    console.error('比对失败:', error)
    toast.show('比对失败')
  }
}

// 确认出库
const confirmOutbound = async () => {
  if (!canConfirm.value) return

  try {
    confirming.value = true

    const response = await submitOutbound({
      kdCode: kdCode.value,
      boxCardCode: boxCardCode.value,
      operator: '操作员001',
    })

    if (response.success) {
      toast.show('出库成功')

      // 重置页面数据
      setTimeout(() => {
        resetPage()
      }, 1500)
    } else {
      toast.show(response.message)
    }
  } catch (error) {
    console.error('出库失败:', error)
    toast.show('出库失败')
  } finally {
    confirming.value = false
  }
}

// 重置页面
const resetPage = () => {
  kdCode.value = ''
  boxCardCode.value = ''
  kdCodeInfo.value = {
    projectCode: '',
    batchCode: '',
    deliveryAddress: '',
  }
  boxCardInfo.value = {
    projectCode: '',
    batchCode: '',
    deliveryAddress: '',
  }
  comparisonResult.value = null
}

// 页面加载时初始化
onMounted(() => {
  // 页面加载时不预填充数据，让用户真正体验输入功能
  console.log('出库校验页面已加载，请扫描或输入KD件码和随箱卡码')
})
</script>

<style scoped>
.outbound-page {
  padding-bottom: 100px;
}

.section-title {
  padding-left: 8px;
  border-left: 4px solid #3b82f6;
}

.scanned-info {
  padding-left: 4px;
}

.info-card {
  padding: 16px;
  background-color: #fafafa;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
}

.info-title {
  font-size: 16px;
}

.info-item {
  margin-bottom: 12px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-label {
  margin-bottom: 4px;
  font-size: 12px;
  color: #666666;
}

.info-input {
  background-color: #ffffff;
}

.comparison-result {
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.result-badge {
  display: inline-flex;
  align-items: center;
  font-size: 14px;
}
</style>
